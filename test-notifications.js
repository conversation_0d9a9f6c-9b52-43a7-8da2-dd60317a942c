// Simple test script to check notifications
const mysql = require('mysql2/promise');

async function testNotifications() {
  let connection;
  try {
    // Create a single connection instead of using the pool
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'rainbow_paws',
      port: 3306
    });

    console.log('Connected to database successfully!');

    // Test admin notifications
    console.log('\n=== ADMIN NOTIFICATIONS ===');
    const adminNotifications = await connection.execute(
      'SELECT * FROM admin_notifications ORDER BY created_at DESC LIMIT 5'
    );
    console.log('Admin notifications found:', adminNotifications[0].length);
    adminNotifications[0].forEach((notif, index) => {
      console.log(`${index + 1}. ${notif.title} - ${notif.message}`);
    });

    // Test user notifications
    console.log('\n=== USER NOTIFICATIONS ===');
    const userNotifications = await connection.execute(
      'SELECT * FROM user_notifications ORDER BY created_at DESC LIMIT 5'
    );
    console.log('User notifications found:', userNotifications[0].length);
    userNotifications[0].forEach((notif, index) => {
      console.log(`${index + 1}. ${notif.title} - ${notif.message}`);
    });

    // Test refunds
    console.log('\n=== REFUNDS ===');
    const refunds = await connection.execute(
      'SELECT * FROM refunds ORDER BY created_at DESC LIMIT 3'
    );
    console.log('Refunds found:', refunds[0].length);
    refunds[0].forEach((refund, index) => {
      console.log(`${index + 1}. Booking ${refund.booking_id} - Status: ${refund.status} - Amount: ${refund.amount}`);
    });

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testNotifications();
