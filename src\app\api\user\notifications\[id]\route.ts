import { NextRequest, NextResponse } from 'next/server';
import { getAuthTokenFromRequest } from '@/utils/auth';
import { query } from '@/lib/db';

/**
 * DELETE - Remove a specific user notification
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get notification ID from params
    const { id } = await params;
    const notificationId = parseInt(id);

    if (isNaN(notificationId)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }

    // Verify user authentication
    const authToken = getAuthTokenFromRequest(request);
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [userId, accountType] = authToken.split('_');
    if (accountType !== 'user') {
      return NextResponse.json({
        error: 'Unauthorized - User access required'
      }, { status: 403 });
    }

    // Check if the notification exists and belongs to the user
    const notificationResult = await query(
      'SELECT id FROM user_notifications WHERE id = ? AND user_id = ?',
      [notificationId, parseInt(userId)]
    ) as any[];

    if (!notificationResult || notificationResult.length === 0) {
      return NextResponse.json({
        error: 'Notification not found or access denied'
      }, { status: 404 });
    }

    // Delete the notification
    await query('DELETE FROM user_notifications WHERE id = ? AND user_id = ?', [
      notificationId,
      parseInt(userId)
    ]);

    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    console.error('Delete user notification error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
