-- Create service_bookings table
CREATE TABLE IF NOT EXISTS `service_bookings` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL,
  `provider_id` INT NOT NULL,
  `package_id` INT NOT NULL,
  `pet_name` VARCHAR(255) NULL,
  `pet_type` VARCHAR(100) NULL,
  `cause_of_death` TEXT NULL,
  `pet_image_url` VARCHAR(255) NULL,
  `booking_date` DATE NULL,
  `booking_time` TIME NULL,
  `status` ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
  `special_requests` TEXT NULL,
  `payment_method` VARCHAR(50) DEFAULT 'cash',
  `payment_status` ENUM('not_paid', 'paid', 'refunded') DEFAULT 'not_paid',
  `delivery_option` ENUM('pickup', 'delivery') DEFAULT 'pickup',
  `delivery_address` TEXT NULL,
  `delivery_distance` FLOAT DEFAULT 0,
  `delivery_fee` DECIMAL(10,2) DEFAULT 0.00,
  `price` DECIMAL(10,2) DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_service_bookings_user` (`user_id` ASC),
  INDEX `idx_service_bookings_provider` (`provider_id` ASC),
  INDEX `idx_service_bookings_package` (`package_id` ASC),
  INDEX `idx_service_bookings_status` (`status` ASC),
  INDEX `idx_service_bookings_date` (`booking_date` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
