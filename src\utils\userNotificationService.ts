import { query } from '@/lib/db';

export interface UserNotificationData {
  userId: number;
  type: string;
  title: string;
  message: string;
  entityType?: string;
  entityId?: number;
}

/**
 * Create a notification for a specific user
 */
export async function createUserNotification({
  userId,
  type,
  title,
  message,
  entityType,
  entityId
}: UserNotificationData): Promise<boolean> {
  try {
    // Ensure the notifications table exists
    await ensureNotificationsTable();

    // Determine link based on notification type
    let link = null;

    if (type === 'refund_processed' || type === 'refund_approved') {
      // Link to the bookings page
      link = '/user/furparent_dashboard/bookings';

      // If we have a specific entity ID, link directly to that booking
      if (entityId) {
        link = `/user/furparent_dashboard/bookings?bookingId=${entityId}`;
      }
    }

    // Insert the notification
    console.log("Inserting user notification with values:", { userId, type, title, message, entityType, entityId, link });

    const result = await query(
      `INSERT INTO notifications (user_id, title, message, type, link)
       VALUES (?, ?, ?, ?, ?)`,
      [userId, title, message, type, link]
    ) as any;

    console.log("User notification created successfully with ID:", result.insertId);
    return true;
  } catch (error) {
    console.error("Error creating user notification:", error);
    return false;
  }
}

/**
 * Ensure the notifications table exists
 */
async function ensureNotificationsTable(): Promise<boolean> {
  try {
    // Check if the table exists
    const tableExists = await query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'notifications'
    `) as any[];

    if (tableExists[0].count === 0) {
      // Create the table if it doesn't exist
      await query(`
        CREATE TABLE IF NOT EXISTS notifications (
          notification_id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          type VARCHAR(50) DEFAULT 'info',
          is_read TINYINT(1) DEFAULT 0,
          link VARCHAR(255) DEFAULT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT current_timestamp(),
          INDEX idx_user_id (user_id),
          INDEX idx_created_at (created_at)
        )
      `);
    }

    return true;
  } catch (error) {
    console.error("Error ensuring notifications table exists:", error);
    return false;
  }
}

/**
 * Get notifications for a specific user
 */
export async function getUserNotifications(userId: number, limit: number = 10): Promise<any[]> {
  try {
    const notifications = await query(`
      SELECT * FROM notifications 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `, [userId, limit]) as any[];

    return notifications || [];
  } catch (error) {
    console.error("Error fetching user notifications:", error);
    return [];
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(notificationId: number, userId: number): Promise<boolean> {
  try {
    await query(`
      UPDATE notifications 
      SET is_read = 1 
      WHERE notification_id = ? AND user_id = ?
    `, [notificationId, userId]);

    return true;
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return false;
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: number): Promise<boolean> {
  try {
    await query(`
      UPDATE notifications 
      SET is_read = 1 
      WHERE user_id = ? AND is_read = 0
    `, [userId]);

    return true;
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    return false;
  }
}
